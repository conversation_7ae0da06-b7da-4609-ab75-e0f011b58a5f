import { generateAnalyticsFromData } from '../analyticsHelpers';

describe('generateAnalyticsFromData', () => {
  // Mock response data
  const mockCurrentPeriodResponses = [
    {
      id: 1,
      created_at: new Date('2024-01-01T10:00:00Z'),
      meta: { completionTime: 120 }, // 2 minutes
    },
    {
      id: 2,
      created_at: new Date('2024-01-02T14:30:00Z'),
      meta: { completionTime: 180 }, // 3 minutes
    },
    {
      id: 3,
      created_at: new Date('2024-01-03T09:15:00Z'),
      meta: { completionTime: 90 }, // 1.5 minutes
    },
  ];

  const mockPreviousPeriodResponses = [
    {
      id: 4,
      created_at: new Date('2023-12-01T10:00:00Z'),
      meta: { completionTime: 150 },
    },
    {
      id: 5,
      created_at: new Date('2023-12-02T14:30:00Z'),
      meta: { completionTime: 200 },
    },
  ];

  describe('Average Completion Time Calculation', () => {
    it('should calculate average completion time correctly', () => {
      const result = generateAnalyticsFromData(mockCurrentPeriodResponses, mockPreviousPeriodResponses, 'vs last week');

      // Expected: (120 + 180 + 90) / 3 = 130 seconds
      expect(result.avgCompletionTime).toBe(130);
    });

    it('should return 0 for average completion time when no responses', () => {
      const result = generateAnalyticsFromData([], [], 'vs last week');
      expect(result.avgCompletionTime).toBe(0);
    });

    it('should return 0 for average completion time when no completion time data', () => {
      const responsesWithoutCompletionTime = [
        {
          id: 1,
          created_at: new Date('2024-01-01T10:00:00Z'),
          meta: {},
        },
      ];

      const result = generateAnalyticsFromData(responsesWithoutCompletionTime, [], 'vs last week');

      expect(result.avgCompletionTime).toBe(0);
    });
  });

  describe('Average Responses Per Day Calculation', () => {
    it('should calculate average responses per day correctly', () => {
      // Create responses that span exactly 3 days (today, yesterday, day before)
      const today = new Date();
      const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
      const dayBefore = new Date(today.getTime() - 2 * 24 * 60 * 60 * 1000);

      const recentResponses = [
        {
          id: 1,
          created_at: dayBefore,
          meta: { completionTime: 120 },
        },
        {
          id: 2,
          created_at: yesterday,
          meta: { completionTime: 180 },
        },
        {
          id: 3,
          created_at: today,
          meta: { completionTime: 90 },
        },
      ];

      const result = generateAnalyticsFromData(recentResponses, [], 'vs last week', 'last-week');

      // The actual calculation depends on the exact time difference
      // Just verify it's a reasonable positive number
      expect(result.avgResponsesPerDay).toBeGreaterThan(0);
      expect(result.avgResponsesPerDay).toBeLessThanOrEqual(3);
    });

    it('should return 0 for average responses per day when no responses', () => {
      const result = generateAnalyticsFromData([], [], 'vs last week', 'last-week');
      expect(result.avgResponsesPerDay).toBe(0);
    });
  });

  describe('Responses By Day Calculation', () => {
    it('should group responses by day correctly', () => {
      const result = generateAnalyticsFromData(mockCurrentPeriodResponses, mockPreviousPeriodResponses, 'vs last week');

      expect(result.responsesByDay).toEqual([
        { date: '2024-01-01', count: 1 },
        { date: '2024-01-02', count: 1 },
        { date: '2024-01-03', count: 1 },
      ]);
    });

    it('should handle multiple responses on the same day', () => {
      const responsesOnSameDay = [
        {
          id: 1,
          created_at: new Date('2024-01-01T10:00:00Z'),
          meta: { completionTime: 120 },
        },
        {
          id: 2,
          created_at: new Date('2024-01-01T14:30:00Z'),
          meta: { completionTime: 180 },
        },
      ];

      const result = generateAnalyticsFromData(responsesOnSameDay, [], 'vs last week', 'last-week');

      expect(result.responsesByDay).toEqual([{ date: '2024-01-01', count: 2 }]);
    });
  });

  describe('Oldest Response Timestamp', () => {
    it('should return the oldest response timestamp', () => {
      const result = generateAnalyticsFromData(mockCurrentPeriodResponses, mockPreviousPeriodResponses, 'vs last week');

      expect(result.oldestResponseTimestamp).toBe('2024-01-01T10:00:00.000Z');
    });

    it('should return null when no responses', () => {
      const result = generateAnalyticsFromData([], [], 'vs last week');
      expect(result.oldestResponseTimestamp).toBeNull();
    });
  });

  describe('Response Rate Trend Components', () => {
    it('should calculate positive trend correctly', () => {
      // Current: 3 responses, Previous: 2 responses = +50% increase
      const result = generateAnalyticsFromData(mockCurrentPeriodResponses, mockPreviousPeriodResponses, 'vs last week', 'last-week');

      expect(result.responseRateTrendComponents).toEqual({
        direction: 'positive',
        magnitude: 50,
        comparisonText: 'vs last week',
      });
    });

    it('should calculate negative trend correctly', () => {
      // Current: 1 response, Previous: 2 responses = -50% decrease
      const oneResponse = [mockCurrentPeriodResponses[0]];
      const result = generateAnalyticsFromData(oneResponse, mockPreviousPeriodResponses, 'vs last week', 'last-week');

      expect(result.responseRateTrendComponents).toEqual({
        direction: 'negative',
        magnitude: 50,
        comparisonText: 'vs last week',
      });
    });

    it('should handle no change scenario', () => {
      // Current: 2 responses, Previous: 2 responses = no change
      // When there's no change, it shows average responses per day instead
      const twoResponses = mockCurrentPeriodResponses.slice(0, 2);
      const result = generateAnalyticsFromData(twoResponses, mockPreviousPeriodResponses, 'vs last week', 'last-week');

      expect(result.responseRateTrendComponents.direction).toBe('neutral');
      expect(result.responseRateTrendComponents.comparisonText).toBe('responses/day avg');
      expect(typeof result.responseRateTrendComponents.magnitude).toBe('number');
    });

    it('should handle new responses scenario (no previous period)', () => {
      const result = generateAnalyticsFromData(mockCurrentPeriodResponses, [], 'vs last week', 'last-week');

      expect(result.responseRateTrendComponents).toEqual({
        direction: 'positive',
        magnitude: 0,
        comparisonText: 'New responses this period',
      });
    });

    it('should handle no responses scenario', () => {
      const result = generateAnalyticsFromData([], [], 'vs last week', 'last-week');

      expect(result.responseRateTrendComponents).toEqual({
        direction: 'neutral',
        magnitude: 0,
        comparisonText: 'No responses yet',
      });
    });

    it('should handle complete drop scenario (previous had responses, current has none)', () => {
      const result = generateAnalyticsFromData([], mockPreviousPeriodResponses, 'vs last week', 'last-week');

      expect(result.responseRateTrendComponents).toEqual({
        direction: 'negative',
        magnitude: 100,
        comparisonText: 'vs last week',
      });
    });
  });

  describe('All-time Filter Special Case', () => {
    it('should calculate average responses per day for all-time filter', () => {
      // Create responses that span exactly 3 days
      const today = new Date();
      const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
      const dayBefore = new Date(today.getTime() - 2 * 24 * 60 * 60 * 1000);

      const recentResponses = [
        {
          id: 1,
          created_at: dayBefore,
          meta: { completionTime: 120 },
        },
        {
          id: 2,
          created_at: yesterday,
          meta: { completionTime: 180 },
        },
        {
          id: 3,
          created_at: today,
          meta: { completionTime: 90 },
        },
      ];

      const result = generateAnalyticsFromData(recentResponses, [], 'responses/day avg', 'all-time');

      expect(result.responseRateTrendComponents.direction).toBe('neutral');
      expect(result.responseRateTrendComponents.comparisonText).toBe('responses/day avg');
      expect(result.responseRateTrendComponents.magnitude).toBeGreaterThan(0);
    });

    it('should handle all-time filter with no responses', () => {
      const result = generateAnalyticsFromData([], [], 'responses/day avg', 'all-time');

      expect(result.responseRateTrendComponents).toEqual({
        direction: 'neutral',
        magnitude: 0,
        comparisonText: 'No responses yet',
      });
    });
  });
});
