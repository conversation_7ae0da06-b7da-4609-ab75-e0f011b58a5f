import { Op } from 'sequelize';

/**
 * Helper functions for processing analytics data
 * These functions contain business logic for time filtering and analytics calculations
 */

export interface TimeFilterResult {
  whereConditions: any;
  previousPeriodConditions?: any;
  trendLabel: string;
}

/**
 * Process time filter and generate where conditions for database queries
 *
 * @param surveyId - Survey ID
 * @param accountId - Account ID
 * @param timeFilter - Time filter string
 * @param customStartDate - Custom start date (for custom range)
 * @param customEndDate - Custom end date (for custom range)
 * @returns Object with where conditions and trend label
 */
export const processTimeFilter = (surveyId: string, accountId: string, timeFilter?: string, customStartDate?: string, customEndDate?: string): TimeFilterResult => {
  // Base where conditions
  const baseConditions = {
    survey_id: surveyId,
    account_id: accountId,
    is_deleted: false,
  };

  let whereConditions: any = { ...baseConditions };
  let previousPeriodConditions: any | undefined;
  let trendLabel = '';

  // Apply time filter if provided
  if (timeFilter && timeFilter !== 'all-time') {
    if (timeFilter === 'custom-range' && customStartDate && customEndDate) {
      // Handle custom date range
      const currentPeriodStart = new Date(customStartDate);
      const currentPeriodEnd = new Date(customEndDate);
      // Set end date to end of day
      currentPeriodEnd.setHours(23, 59, 59, 999);

      whereConditions.created_at = {
        [Op.between]: [currentPeriodStart, currentPeriodEnd],
      };

      // Calculate previous period of same duration
      const rangeDuration = currentPeriodEnd.getTime() - currentPeriodStart.getTime();
      const previousPeriodEnd = new Date(currentPeriodStart.getTime() - 1); // 1ms before current period
      const previousPeriodStart = new Date(previousPeriodEnd.getTime() - rangeDuration);

      previousPeriodConditions = {
        ...baseConditions,
        created_at: {
          [Op.between]: [previousPeriodStart, previousPeriodEnd],
        },
      };

      trendLabel = 'vs previous period';
    } else {
      // Handle predefined time filters
      const now = new Date();
      let currentPeriodStart: Date;
      let previousPeriodStart: Date;
      let previousPeriodEnd: Date;

      switch (timeFilter) {
        case 'last-24-hours':
          currentPeriodStart = new Date(now.getTime() - 24 * 60 * 60 * 1000);
          previousPeriodEnd = new Date(currentPeriodStart.getTime() - 1);
          previousPeriodStart = new Date(previousPeriodEnd.getTime() - 24 * 60 * 60 * 1000);
          trendLabel = 'vs yesterday';
          break;
        case '7-days':
          currentPeriodStart = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          previousPeriodEnd = new Date(currentPeriodStart.getTime() - 1);
          previousPeriodStart = new Date(previousPeriodEnd.getTime() - 7 * 24 * 60 * 60 * 1000);
          trendLabel = 'vs last week';
          break;
        case '30-days':
          currentPeriodStart = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          previousPeriodEnd = new Date(currentPeriodStart.getTime() - 1);
          previousPeriodStart = new Date(previousPeriodEnd.getTime() - 30 * 24 * 60 * 60 * 1000);
          trendLabel = 'vs last month';
          break;
        case '90-days':
          currentPeriodStart = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
          previousPeriodEnd = new Date(currentPeriodStart.getTime() - 1);
          previousPeriodStart = new Date(previousPeriodEnd.getTime() - 90 * 24 * 60 * 60 * 1000);
          trendLabel = 'vs previous quarter';
          break;
        default:
          currentPeriodStart = new Date(0);
          previousPeriodStart = new Date(0);
          previousPeriodEnd = new Date(0);
          trendLabel = '';
      }

      whereConditions.created_at = {
        [Op.gte]: currentPeriodStart,
      };

      if (trendLabel) {
        previousPeriodConditions = {
          ...baseConditions,
          created_at: {
            [Op.between]: [previousPeriodStart, previousPeriodEnd],
          },
        };
      }
    }
  }

  return {
    whereConditions,
    previousPeriodConditions,
    trendLabel,
  };
};

/**
 * Generate analytics from raw response data
 *
 * @param currentPeriodResponses - Array of current period response data
 * @param previousPeriodResponses - Array of previous period response data
 * @param trendLabel - Label for trend comparison
 * @param timeFilter - Original time filter for special handling
 * @returns Analytics object
 */
export const generateAnalyticsFromData = (currentPeriodResponses: any[], previousPeriodResponses: any[], trendLabel: string, timeFilter?: string) => {
  let avgCompletionTime = 0;
  let avgResponsesPerDay = 0;
  const responsesByDay: any = {};

  // Process current period responses
  currentPeriodResponses.forEach(response => {
    // Calculate completion time if available in meta
    if (response.meta?.completionTime) {
      avgCompletionTime += response.meta.completionTime;
    }

    // Group by day for chart data
    const dayKey = response.created_at.toISOString().split('T')[0];
    responsesByDay[dayKey] = (responsesByDay[dayKey] || 0) + 1;
  });

  // Calculate average completion time
  if (currentPeriodResponses.length > 0 && avgCompletionTime > 0) {
    avgCompletionTime = Math.round(avgCompletionTime / currentPeriodResponses.length);
  }

  // Calculate average responses per day
  if (currentPeriodResponses.length > 0) {
    const firstResponseDate = new Date(currentPeriodResponses[0].created_at);
    const daysSinceFirst = Math.max(1, Math.ceil((new Date().getTime() - firstResponseDate.getTime()) / (24 * 60 * 60 * 1000)));
    avgResponsesPerDay = parseFloat((currentPeriodResponses.length / daysSinceFirst).toFixed(1));
  }

  // Calculate response rate trend - broken into components for better frontend styling
  let responseRateTrendComponents = {
    direction: 'neutral' as 'positive' | 'negative' | 'neutral',
    magnitude: 0,
    comparisonText: '',
  };

  if (timeFilter && timeFilter !== 'all-time' && trendLabel) {
    const previousPeriodCount = previousPeriodResponses.length;

    // Calculate trend with edge case handling
    if (previousPeriodCount === 0 && currentPeriodResponses.length === 0) {
      responseRateTrendComponents = {
        direction: 'neutral',
        magnitude: 0,
        comparisonText: 'No responses yet',
      };
    } else if (previousPeriodCount === 0 && currentPeriodResponses.length > 0) {
      responseRateTrendComponents = {
        direction: 'positive',
        magnitude: 0,
        comparisonText: 'New responses this period',
      };
    } else if (previousPeriodCount > 0 && currentPeriodResponses.length === 0) {
      responseRateTrendComponents = {
        direction: 'negative',
        magnitude: 100,
        comparisonText: trendLabel,
      };
    } else if (previousPeriodCount === currentPeriodResponses.length) {
      // For neutral (no change), show average responses per day instead
      responseRateTrendComponents = {
        direction: 'neutral',
        magnitude: avgResponsesPerDay,
        comparisonText: 'responses/day avg',
      };
    } else {
      const percentChange = Math.round(((currentPeriodResponses.length - previousPeriodCount) / previousPeriodCount) * 100);
      responseRateTrendComponents = {
        direction: percentChange > 0 ? 'positive' : 'negative',
        magnitude: Math.abs(percentChange),
        comparisonText: trendLabel,
      };
    }
  } else if (timeFilter === 'all-time') {
    // For all-time, show average responses per day
    if (currentPeriodResponses.length > 0) {
      const firstResponseDate = new Date(currentPeriodResponses[0].created_at);
      const daysSinceFirst = Math.max(1, Math.ceil((new Date().getTime() - firstResponseDate.getTime()) / (24 * 60 * 60 * 1000)));
      const avgPerDay = (currentPeriodResponses.length / daysSinceFirst).toFixed(1);
      responseRateTrendComponents = {
        direction: 'neutral',
        magnitude: parseFloat(avgPerDay),
        comparisonText: 'responses/day avg',
      };
    } else {
      responseRateTrendComponents = {
        direction: 'neutral',
        magnitude: 0,
        comparisonText: 'No responses yet',
      };
    }
  }

  // Convert responsesByDay object to array format with dates and counts
  const responsesByDayArray = Object.entries(responsesByDay)
    .map(([date, count]) => ({
      date,
      count,
    }))
    .sort((a, b) => a.date.localeCompare(b.date)); // Sort by date ascending

  // Get the oldest response timestamp for date range setting
  const oldestResponseTimestamp = currentPeriodResponses.length > 0 ? currentPeriodResponses[0].created_at.toISOString() : null;

  return {
    avgCompletionTime,
    avgResponsesPerDay,
    responsesByDay: responsesByDayArray,
    oldestResponseTimestamp,
    responseRateTrendComponents,
  };
};
