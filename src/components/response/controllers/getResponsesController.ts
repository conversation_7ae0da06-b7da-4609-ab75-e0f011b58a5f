import { Request, Response } from 'express';

import { fetchResponsesWithConditions, fetchAnalyticsData } from '../dals';
import { verifyResourceOwnership } from '../../security/helpers';
import { processTimeFilter, generateAnalyticsFromData } from '../helpers/analyticsHelpers';
import { logger } from '../../../global/services';

export const getResponsesController = async (req: Request, res: Response) => {
  const surveyId = res.locals.surveyId;
  const accountId = res.locals.accountId;

  // Extract query parameters for filtering
  const timeFilter = req.query.timeFilter as string;
  const customStartDate = req.query.customStartDate as string;
  const customEndDate = req.query.customEndDate as string;
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 10;

  logger.info('Responses request', {
    surveyId: surveyId.substring(0, 8) + '...',
    accountId: accountId.substring(0, 8) + '...',
    filters: { timeFilter, customStartDate, customEndDate, page, limit },
    ip: req.ip,
  });

  // SECURITY FIX: Double authorization check - middleware + controller level
  const isOwner = await verifyResourceOwnership('survey', surveyId, accountId);
  if (!isOwner) {
    logger.warn('Unauthorized responses access attempt', {
      surveyId: surveyId.substring(0, 8) + '...',
      accountId: accountId.substring(0, 8) + '...',
      ip: req.ip,
      userAgent: req.headers['user-agent'],
    });
    return res.status(403).json({
      success: false,
      message: 'Access denied',
    });
  }

  // Process time filter and generate where conditions
  const { whereConditions, previousPeriodConditions, trendLabel } = processTimeFilter(surveyId, accountId, timeFilter, customStartDate, customEndDate);

  // Fetch responses with pagination
  const responsesResult = await fetchResponsesWithConditions(whereConditions, page, limit);

  if (!responsesResult.success) {
    return res.status(400).json({
      success: false,
      message: responsesResult.message,
    });
  }

  // Fetch analytics data
  const analyticsResult = await fetchAnalyticsData(whereConditions, previousPeriodConditions);

  if (!analyticsResult.success) {
    return res.status(400).json({
      success: false,
      message: analyticsResult.message,
    });
  }

  // Generate analytics from raw data
  const analyticsData = analyticsResult.payload as any;
  const analytics = generateAnalyticsFromData(analyticsData.currentPeriodResponses, analyticsData.previousPeriodResponses, trendLabel, timeFilter);

  // Combine responses and analytics
  const responsesData = responsesResult.payload as any;
  const finalPayload = {
    ...responsesData,
    totalResponses: analytics.totalResponses,
    avgCompletionTime: analytics.avgCompletionTime,
    oldestResponseTimestamp: analytics.oldestResponseTimestamp,
    responsesByDay: analytics.responsesByDay,
    responseTimestamps: analytics.responseTimestamps,
    responseRateTrend: analytics.responseRateTrend, // Keep for backward compatibility
    responseRateTrendComponents: analytics.responseRateTrendComponents, // New structured format
  };

  return res.status(200).json({
    success: true,
    message: responsesResult.message,
    payload: finalPayload,
  });
};
